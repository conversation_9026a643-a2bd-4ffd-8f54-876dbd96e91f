{"compilerOptions": {"target": "ESNext", "lib": ["DOM", "DOM.Iterable", "ESNext"], "module": "ESNext", "skipLibCheck": true, "types": ["vite/client", "vitest/globals", "node", "@testing-library/jest-dom"], "jsx": "react-jsx", "allowSyntheticDefaultImports": true, "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true}, "include": ["src"]}
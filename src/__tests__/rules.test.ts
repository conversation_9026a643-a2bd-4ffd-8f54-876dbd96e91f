import { Engine } from 'json-rules-engine';

test('Test rules', async () => {
    const event = {
        type: 'multiplyLtvRate',
        params: {
            value: 0.85,
        },
    };
    const facts = {
        propertyTenure: '',
        propertyType: 'Flat',
        propertyOwnershipType: '',
        propertyValue: '',
    };
    const rules = [
        {
            event,
            conditions: {
                any: [
                    {
                        fact: 'propertyType',
                        value: 'Flat',
                        operator: 'equal',
                    },
                    {
                        fact: 'propertyType',
                        value: 'Maisonette',
                        operator: 'equal',
                    },
                    {
                        fact: 'propertyTenure',
                        value: 'Leasehold',
                        operator: 'equal',
                    },
                ],
            },
        },
    ];

    const engine = new Engine();
    rules.forEach((rule) => engine.addRule(rule));
    const result = await engine.run(facts);
    expect(result.events).toEqual([event]);
});
``